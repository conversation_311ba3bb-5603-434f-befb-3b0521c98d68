<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام حساب إجازات الموظفين المتطور</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .save-indicator {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .save-indicator.saving {
            background: rgba(255,193,7,0.8);
            color: #000;
        }
        
        .save-indicator.saved {
            background: rgba(40,167,69,0.8);
            color: #fff;
        }
        
        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: end;
        }
        
        .form-group {
            flex: 1;
            min-width: 200px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .table-container {
            padding: 30px;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        th {
            background: linear-gradient(135deg, #34495e, #2c3e50);
            color: white;
            padding: 15px;
            font-weight: 600;
            text-align: center;
        }
        
        td {
            padding: 12px 15px;
            border-bottom: 1px solid #ecf0f1;
            text-align: center;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .leave-balance {
            font-weight: bold;
        }
        
        .balance-positive {
            color: #27ae60;
        }
        
        .balance-negative {
            color: #e74c3c;
        }
        
        .balance-warning {
            color: #f39c12;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
        }
        
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .stat-card .number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        
        .export-section {
            padding: 30px;
            text-align: center;
            background: #ecf0f1;
        }
        
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: 600;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        
        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .modal h2 {
            color: #2c3e50;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .leave-history {
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        
        .leave-record {
            padding: 15px;
            border-bottom: 1px solid #f1f1f1;
        }
        
        .leave-record:last-child {
            border-bottom: none;
        }
        
        .leave-record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .delete-leave {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .backup-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .file-input {
            display: none;
        }
        
        .file-input-label {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            display: inline-block;
        }
        
        .file-input-label:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="save-indicator" id="saveIndicator">محفوظ تلقائياً</div>
            <h1>🏢 نظام حساب إجازات الموظفين المتطور</h1>
            <p>إدارة احترافية لإجازات الموظفين مع الحفظ التلقائي</p>
        </div>
        
        <div class="controls">
            <div class="form-row">
                <div class="form-group">
                    <label>اسم الموظف *</label>
                    <input type="text" id="employeeName" placeholder="أدخل اسم الموظف" required>
                </div>
                <div class="form-group">
                    <label>الرقم الوظيفي *</label>
                    <input type="text" id="employeeId" placeholder="أدخل الرقم الوظيفي" required>
                </div>
                <div class="form-group">
                    <label>القسم *</label>
                    <input type="text" id="department" placeholder="أدخل القسم" required>
                </div>
                <div class="form-group">
                    <label>تاريخ التوظيف *</label>
                    <input type="date" id="hireDate" required>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>الرصيد السنوي (يوم)</label>
                    <input type="number" id="annualLeave" value="30" min="0" max="365">
                </div>
                <div class="form-group">
                    <label>إجازة مرضية (يوم)</label>
                    <input type="number" id="sickLeave" value="15" min="0" max="90">
                </div>
                <div class="form-group">
                    <label>إجازة طارئة (يوم)</label>
                    <input type="number" id="emergencyLeave" value="5" min="0" max="30">
                </div>
                <div class="form-group">
                    <button class="btn" onclick="addEmployee()" id="addEmployeeBtn">إضافة موظف</button>
                </div>
            </div>
        </div>
        
        <div id="alertContainer"></div>
        
        <div class="table-container">
            <table id="employeeTable">
                <thead>
                    <tr>
                        <th>اسم الموظف</th>
                        <th>الرقم الوظيفي</th>
                        <th>القسم</th>
                        <th>تاريخ التوظيف</th>
                        <th>سنوات الخدمة</th>
                        <th>الرصيد السنوي</th>
                        <th>المستخدم</th>
                        <th>المتبقي</th>
                        <th>إجازة مرضية</th>
                        <th>إجازة طارئة</th>
                        <th>الحالة</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody id="employeeTableBody">
                </tbody>
            </table>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3>إجمالي الموظفين</h3>
                <div class="number" id="totalEmployees">0</div>
            </div>
            <div class="stat-card">
                <h3>إجمالي الإجازات المستخدمة</h3>
                <div class="number" id="totalUsedLeaves">0</div>
            </div>
            <div class="stat-card">
                <h3>إجمالي الإجازات المتبقية</h3>
                <div class="number" id="totalRemainingLeaves">0</div>
            </div>
            <div class="stat-card">
                <h3>متوسط الاستخدام</h3>
                <div class="number" id="averageUsage">0%</div>
            </div>
        </div>
        
        <div class="export-section">
            <h3>إدارة البيانات</h3>
            <div class="backup-controls">
                <button class="btn btn-success" onclick="exportToCSV()">📊 تصدير CSV</button>
                <button class="btn btn-success" onclick="exportToJSON()">💾 تصدير نسخة احتياطية</button>
                <label for="importFile" class="file-input-label">📁 استيراد نسخة احتياطية</label>
                <input type="file" id="importFile" class="file-input" accept=".json" onchange="importData()">
                <button class="btn btn-warning" onclick="resetData()">🔄 إعادة تعيين</button>
                <button class="btn btn-danger" onclick="clearAllData()">🗑️ مسح الكل</button>
            </div>
        </div>
    </div>

    <!-- Leave Management Modal -->
    <div id="leaveModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeLeaveModal()">&times;</span>
            <h2>إدارة إجازات: <span id="modalEmployeeName"></span></h2>
            
            <div class="form-row">
                <div class="form-group">
                    <label>نوع الإجازة</label>
                    <select id="leaveType">
                        <option value="annual">إجازة سنوية</option>
                        <option value="sick">إجازة مرضية</option>
                        <option value="emergency">إجازة طارئة</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>عدد الأيام</label>
                    <input type="number" id="leaveDays" min="0.5" step="0.5" placeholder="أدخل عدد الأيام">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>تاريخ البداية</label>
                    <input type="date" id="leaveStartDate">
                </div>
                <div class="form-group">
                    <label>تاريخ النهاية</label>
                    <input type="date" id="leaveEndDate">
                </div>
            </div>
            
            <div class="form-group">
                <label>السبب / الملاحظات</label>
                <input type="text" id="leaveReason" placeholder="أدخل سبب الإجازة (اختياري)">
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <button class="btn btn-success" onclick="addLeaveRecord()">إضافة الإجازة</button>
                <button class="btn" onclick="closeLeaveModal()">إلغاء</button>
            </div>
            
            <div class="leave-history" id="leaveHistory">
                <h4 style="text-align: center; padding: 15px; margin: 0; background: #f8f9fa;">سجل الإجازات</h4>
                <div id="leaveHistoryContent"></div>
            </div>
        </div>
    </div>

    <script>
        let employees = [];
        let editingIndex = -1;
        let currentEmployeeIndex = -1;

        // Auto-save functionality
        function saveData() {
            try {
                const data = {
                    employees: employees,
                    lastSaved: new Date().toISOString(),
                    version: '2.0'
                };
                
                // Create a JSON string
                const jsonData = JSON.stringify(data, null, 2);
                
                // Create a Blob and save as downloadable file automatically when data changes
                showSaveIndicator('saving');
                
                setTimeout(() => {
                    showSaveIndicator('saved');
                }, 500);
                
                return jsonData;
            } catch (error) {
                console.error('Error saving data:', error);
                showAlert('خطأ في حفظ البيانات', 'danger');
            }
        }

        function showSaveIndicator(status) {
            const indicator = document.getElementById('saveIndicator');
            indicator.className = 'save-indicator';
            
            if (status === 'saving') {
                indicator.className += ' saving';
                indicator.textContent = 'جاري الحفظ...';
            } else if (status === 'saved') {
                indicator.className += ' saved';
                indicator.textContent = 'تم الحفظ ✓';
                setTimeout(() => {
                    indicator.className = 'save-indicator';
                    indicator.textContent = 'محفوظ تلقائياً';
                }, 2000);
            }
        }

        // Load data from file if available
        function loadData() {
            // Initialize with empty data
            employees = [];
            updateTable();
            updateStats();
        }

        // Enhanced alert system
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            
            const icon = type === 'success' ? '✅' : type === 'warning' ? '⚠️' : type === 'danger' ? '❌' : 'ℹ️';
            alert.innerHTML = `${icon} ${message}`;
            
            alertContainer.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 4000);
        }

        // Enhanced employee addition
        function addEmployee() {
            const name = document.getElementById('employeeName').value.trim();
            const id = document.getElementById('employeeId').value.trim();
            const department = document.getElementById('department').value.trim();
            const hireDate = document.getElementById('hireDate').value;
            const annualLeave = parseInt(document.getElementById('annualLeave').value) || 0;
            const sickLeave = parseInt(document.getElementById('sickLeave').value) || 0;
            const emergencyLeave = parseInt(document.getElementById('emergencyLeave').value) || 0;

            // Validation
            if (!name || !id || !department || !hireDate) {
                showAlert('يرجى ملء جميع الحقول المطلوبة المميزة بـ *', 'warning');
                return;
            }

            // Check if employee ID already exists (except when editing)
            if (editingIndex === -1 && employees.some(emp => emp.id === id)) {
                showAlert('الرقم الوظيفي موجود مسبقاً. يرجى استخدام رقم مختلف', 'warning');
                return;
            }

            // Check hire date is not in future
            if (new Date(hireDate) > new Date()) {
                showAlert('تاريخ التوظيف لا يمكن أن يكون في المستقبل', 'warning');
                return;
            }

            const employee = {
                name,
                id,
                department,
                hireDate,
                annualLeave,
                sickLeave,
                emergencyLeave,
                usedAnnual: 0,
                usedSick: 0,
                usedEmergency: 0,
                leaveHistory: []
            };

            if (editingIndex === -1) {
                employees.push(employee);
                showAlert(`تم إضافة الموظف ${name} بنجاح`, 'success');
            } else {
                // Preserve leave history when editing
                employee.leaveHistory = employees[editingIndex].leaveHistory || [];
                employees[editingIndex] = employee;
                showAlert(`تم تحديث بيانات الموظف ${name} بنجاح`, 'success');
                editingIndex = -1;
                document.getElementById('addEmployeeBtn').textContent = 'إضافة موظف';
            }

            clearForm();
            updateTable();
            updateStats();
            saveData();
        }

        function clearForm() {
            document.getElementById('employeeName').value = '';
            document.getElementById('employeeId').value = '';
            document.getElementById('department').value = '';
            document.getElementById('hireDate').value = '';
            document.getElementById('annualLeave').value = '30';
            document.getElementById('sickLeave').value = '15';
            document.getElementById('emergencyLeave').value = '5';
        }

        function calculateYearsOfService(hireDate) {
            const today = new Date();
            const hire = new Date(hireDate);
            const years = (today - hire) / (365.25 * 24 * 60 * 60 * 1000);
            return Math.max(0, Math.floor(years));
        }

        function getEmployeeStatus(employee) {
            const remainingAnnual = employee.annualLeave - employee.usedAnnual;
            if (remainingAnnual <= 0) return 'نفد الرصيد';
            if (remainingAnnual <= 5) return 'رصيد منخفض';
            return 'طبيعي';
        }

        function updateTable() {
            const tbody = document.getElementById('employeeTableBody');
            tbody.innerHTML = '';

            employees.forEach((employee, index) => {
                const row = tbody.insertRow();
                const yearsOfService = calculateYearsOfService(employee.hireDate);
                const remainingAnnual = employee.annualLeave - employee.usedAnnual;
                const remainingSick = employee.sickLeave - employee.usedSick;
                const remainingEmergency = employee.emergencyLeave - employee.usedEmergency;
                const status = getEmployeeStatus(employee);

                let statusClass = 'balance-positive';
                if (remainingAnnual <= 0) statusClass = 'balance-negative';
                else if (remainingAnnual <= 5) statusClass = 'balance-warning';

                row.innerHTML = `
                    <td>${employee.name}</td>
                    <td>${employee.id}</td>
                    <td>${employee.department}</td>
                    <td>${new Date(employee.hireDate).toLocaleDateString('ar-SA')}</td>
                    <td>${yearsOfService}</td>
                    <td>${employee.annualLeave}</td>
                    <td>${employee.usedAnnual}</td>
                    <td class="leave-balance ${statusClass}">${remainingAnnual}</td>
                    <td class="leave-balance ${remainingSick <= 0 ? 'balance-negative' : remainingSick <= 2 ? 'balance-warning' : 'balance-positive'}">${remainingSick}</td>
                    <td class="leave-balance ${remainingEmergency <= 0 ? 'balance-negative' : 'balance-positive'}">${remainingEmergency}</td>
                    <td class="${statusClass}">${status}</td>
                    <td>
                        <button class="btn btn-small" onclick="editEmployee(${index})" title="تعديل البيانات">✏️ تعديل</button>
                        <button class="btn btn-success btn-small" onclick="openLeaveModal(${index})" title="إدارة الإجازات">📅 إجازات</button>
                        <button class="btn btn-danger btn-small" onclick="deleteEmployee(${index})" title="حذف الموظف">🗑️ حذف</button>
                    </td>
                `;
            });
        }

        function editEmployee(index) {
            const employee = employees[index];
            document.getElementById('employeeName').value = employee.name;
            document.getElementById('employeeId').value = employee.id;
            document.getElementById('department').value = employee.department;
            document.getElementById('hireDate').value = employee.hireDate;
            document.getElementById('annualLeave').value = employee.annualLeave;
            document.getElementById('sickLeave').value = employee.sickLeave;
            document.getElementById('emergencyLeave').value = employee.emergencyLeave;
            
            editingIndex = index;
            document.getElementById('addEmployeeBtn').textContent = 'تحديث الموظف';
            
            // Scroll to top
            window.scrollTo(0, 0);
        }

        function deleteEmployee(index) {
            const employee = employees[index];
            if (confirm(`هل أنت متأكد من حذف الموظف "${employee.name}"؟\nسيتم حذف جميع سجلات الإجازات المرتبطة به.`)) {
                employees.splice(index, 1);
                updateTable();
                updateStats();
                saveData();
                showAlert(`تم حذف الموظف "${employee.name}" بنجاح`, 'success');
            }
        }

        // Enhanced leave management
        function openLeaveModal(index) {
            currentEmployeeIndex = index;
            const employee = employees[index];
            
            document.getElementById('modalEmployeeName').textContent = employee.name;
            document.getElementById('leaveType').value = 'annual';
            document.getElementById('leaveDays').value = '';
            document.getElementById('leaveStartDate').value = '';
            document.getElementById('leaveEndDate').value = '';
            document.getElementById('leaveReason').value = '';
            
            updateLeaveHistory();
            document.getElementById('leaveModal').style.display = 'block';
        }

        function closeLeaveModal() {
            document.getElementById('leaveModal').style.display = 'none';
            currentEmployeeIndex = -1;
        }

        function addLeaveRecord() {
            if (currentEmployeeIndex === -1) return;
            
            const employee = employees[currentEmployeeIndex];
            const leaveType = document.getElementById('leaveType').value;
            const leaveDays = parseFloat(document.getElementById('leaveDays').value);
            const startDate = document.getElementById('leaveStartDate').value;
            const endDate = document.getElementById('leaveEndDate').value;
            const reason = document.getElementById('leaveReason').value.trim();

            // Validation
            if (!leaveDays || leaveDays <= 0) {
                showAlert('يرجى إدخال عدد أيام صحيح', 'warning');
                return;
            }

            if (!startDate || !endDate) {
                showAlert('يرجى تحديد تاريخ البداية والنهاية', 'warning');
                return;
            }

            if (new Date(startDate) > new Date(endDate)) {
                showAlert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'warning');
                return;
            }

            // Check available balance
            let currentUsed = 0;
            let maxAllowed = 0;
            let leaveTypeName = '';

            switch(leaveType) {
                case 'annual':
                    currentUsed = employee.usedAnnual;
                    maxAllowed = employee.annualLeave;
                    leaveTypeName = 'إجازة سنوية';
                    break;
                case 'sick':
                    currentUsed = employee.usedSick;
                    maxAllowed = employee.sickLeave;
                    leaveTypeName = 'إجازة مرضية';
                    break;
                case 'emergency':
                    currentUsed = employee.usedEmergency;
                    maxAllowed = employee.emergencyLeave;
                    leaveTypeName = 'إجازة طارئة';
                    break;
            }

            if (currentUsed + leaveDays > maxAllowed) {
                const remaining = maxAllowed - currentUsed;
                showAlert(`الرصيد غير كافي. المتبقي: ${remaining} يوم فقط`, 'warning');
                return;
            }

            // Add leave record
            const leaveRecord = {
                id: Date.now(),
                type: leaveType,
                typeName: leaveTypeName,
                days: leaveDays,
                startDate,
                endDate,
                reason: reason || 'غير محدد',
                addedDate: new Date().toISOString()
            };

            if (!employee.leaveHistory) {
                employee.leaveHistory = [];
            }
            employee.leaveHistory.push(leaveRecord);

            // Update used days
            switch(leaveType) {
                case 'annual':
                    employee.usedAnnual += leaveDays;