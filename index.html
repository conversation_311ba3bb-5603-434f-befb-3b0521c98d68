<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام حساب إجازات الموظفين المتطور</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .save-indicator {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .save-indicator.saving {
            background: rgba(255,193,7,0.8);
            color: #000;
        }
        
        .save-indicator.saved {
            background: rgba(40,167,69,0.8);
            color: #fff;
        }
        
        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: end;
        }
        
        .form-group {
            flex: 1;
            min-width: 200px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .table-container {
            padding: 30px;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        th {
            background: linear-gradient(135deg, #34495e, #2c3e50);
            color: white;
            padding: 15px;
            font-weight: 600;
            text-align: center;
        }
        
        td {
            padding: 12px 15px;
            border-bottom: 1px solid #ecf0f1;
            text-align: center;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .leave-balance {
            font-weight: bold;
        }
        
        .balance-positive {
            color: #27ae60;
        }
        
        .balance-negative {
            color: #e74c3c;
        }
        
        .balance-warning {
            color: #f39c12;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
        }
        
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .stat-card .number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        
        .export-section {
            padding: 30px;
            text-align: center;
            background: #ecf0f1;
        }
        
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: 600;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        
        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .modal h2 {
            color: #2c3e50;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .leave-history {
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        
        .leave-record {
            padding: 15px;
            border-bottom: 1px solid #f1f1f1;
        }
        
        .leave-record:last-child {
            border-bottom: none;
        }
        
        .leave-record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .delete-leave {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .backup-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .file-input {
            display: none;
        }
        
        .file-input-label {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            display: inline-block;
        }
        
        .file-input-label:hover {
            transform: translateY(-2px);
        }

        /* Search and Filter Styles */
        .search-filter-section {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
        }

        .search-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .search-group {
            flex: 1;
            min-width: 200px;
        }

        .search-input {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 14px;
            transition: all 0.3s;
            background: white;
        }

        .search-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            min-width: 150px;
        }

        .clear-filters {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
        }

        .results-info {
            background: rgba(52, 152, 219, 0.1);
            padding: 10px 15px;
            border-radius: 8px;
            color: #2c3e50;
            font-weight: 600;
            text-align: center;
        }

        /* Dark Mode Styles */
        .dark-mode-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s;
        }

        .dark-mode-toggle:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        body.dark-mode {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        body.dark-mode .container {
            background: #34495e;
            color: #ecf0f1;
        }

        body.dark-mode .controls,
        body.dark-mode .search-filter-section {
            background: #2c3e50;
        }

        body.dark-mode .form-group input,
        body.dark-mode .form-group select,
        body.dark-mode .search-input,
        body.dark-mode .filter-select {
            background: #34495e;
            color: #ecf0f1;
            border-color: #4a5f7a;
        }

        body.dark-mode table {
            background: #34495e;
            color: #ecf0f1;
        }

        body.dark-mode td {
            border-bottom-color: #4a5f7a;
        }

        body.dark-mode tr:hover {
            background: #2c3e50;
        }

        body.dark-mode .stat-card {
            background: #2c3e50;
            color: #ecf0f1;
        }

        body.dark-mode .modal-content {
            background: #34495e;
            color: #ecf0f1;
        }

        body.dark-mode .leave-record {
            border-bottom-color: #4a5f7a;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .form-row, .search-row {
                flex-direction: column;
            }

            .form-group, .search-group {
                min-width: 100%;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .stats {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            table {
                font-size: 12px;
            }

            th, td {
                padding: 8px 5px;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Tooltip Styles */
        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #555;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        /* Print Styles */
        @media print {
            .controls, .export-section, .search-filter-section {
                display: none;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
            }

            body {
                background: white;
            }

            .btn {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="save-indicator" id="saveIndicator">محفوظ تلقائياً</div>
            <button class="dark-mode-toggle" onclick="toggleDarkMode()" title="تبديل الوضع المظلم">🌙</button>
            <h1>🏢 نظام حساب إجازات الموظفين المتطور</h1>
            <p>إدارة احترافية لإجازات الموظفين مع الحفظ التلقائي</p>
        </div>
        
        <div class="controls">
            <div class="form-row">
                <div class="form-group">
                    <label>اسم الموظف *</label>
                    <input type="text" id="employeeName" placeholder="أدخل اسم الموظف" required>
                </div>
                <div class="form-group">
                    <label>الرقم الوظيفي *</label>
                    <input type="text" id="employeeId" placeholder="أدخل الرقم الوظيفي" required>
                </div>
                <div class="form-group">
                    <label>القسم *</label>
                    <input type="text" id="department" placeholder="أدخل القسم" required>
                </div>
                <div class="form-group">
                    <label>تاريخ التوظيف *</label>
                    <input type="date" id="hireDate" required>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>الرصيد السنوي (يوم)</label>
                    <input type="number" id="annualLeave" value="30" min="0" max="365">
                </div>
                <div class="form-group">
                    <label>إجازة مرضية (يوم)</label>
                    <input type="number" id="sickLeave" value="15" min="0" max="90">
                </div>
                <div class="form-group">
                    <label>إجازة طارئة (يوم)</label>
                    <input type="number" id="emergencyLeave" value="5" min="0" max="30">
                </div>
                <div class="form-group">
                    <button class="btn" onclick="addEmployee()" id="addEmployeeBtn">إضافة موظف</button>
                </div>
            </div>
        </div>
        
        <div id="alertContainer"></div>

        <!-- Search and Filter Section -->
        <div class="search-filter-section">
            <div class="search-row">
                <div class="search-group">
                    <label>البحث في الموظفين</label>
                    <input type="text" class="search-input" id="searchInput" placeholder="ابحث بالاسم، الرقم الوظيفي، أو القسم..." onkeyup="searchEmployees()">
                </div>
                <div class="search-group">
                    <label>فلترة حسب القسم</label>
                    <select class="filter-select" id="departmentFilter" onchange="filterEmployees()">
                        <option value="">جميع الأقسام</option>
                    </select>
                </div>
                <div class="search-group">
                    <label>فلترة حسب الحالة</label>
                    <select class="filter-select" id="statusFilter" onchange="filterEmployees()">
                        <option value="">جميع الحالات</option>
                        <option value="طبيعي">طبيعي</option>
                        <option value="رصيد منخفض">رصيد منخفض</option>
                        <option value="نفد الرصيد">نفد الرصيد</option>
                    </select>
                </div>
                <div class="search-group">
                    <button class="clear-filters" onclick="clearFilters()">مسح الفلاتر</button>
                </div>
            </div>
            <div class="results-info" id="resultsInfo">
                عرض جميع الموظفين
            </div>
        </div>
        
        <div class="table-container">
            <table id="employeeTable">
                <thead>
                    <tr>
                        <th>اسم الموظف</th>
                        <th>الرقم الوظيفي</th>
                        <th>القسم</th>
                        <th>تاريخ التوظيف</th>
                        <th>سنوات الخدمة</th>
                        <th>الرصيد السنوي</th>
                        <th>المستخدم</th>
                        <th>المتبقي</th>
                        <th>إجازة مرضية</th>
                        <th>إجازة طارئة</th>
                        <th>الحالة</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody id="employeeTableBody">
                </tbody>
            </table>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3>إجمالي الموظفين</h3>
                <div class="number" id="totalEmployees">0</div>
            </div>
            <div class="stat-card">
                <h3>إجمالي الإجازات المستخدمة</h3>
                <div class="number" id="totalUsedLeaves">0</div>
            </div>
            <div class="stat-card">
                <h3>إجمالي الإجازات المتبقية</h3>
                <div class="number" id="totalRemainingLeaves">0</div>
            </div>
            <div class="stat-card">
                <h3>متوسط الاستخدام</h3>
                <div class="number" id="averageUsage">0%</div>
            </div>
        </div>
        
        <div class="export-section">
            <h3>إدارة البيانات والتقارير</h3>
            <div class="backup-controls">
                <button class="btn btn-success" onclick="exportToCSV()">📊 تصدير CSV</button>
                <button class="btn btn-success" onclick="exportToJSON()">💾 تصدير نسخة احتياطية</button>
                <button class="btn btn-success" onclick="generateAdvancedReport()">📈 تقرير متقدم</button>
                <button class="btn btn-success" onclick="exportLeaveHistory()">📋 تصدير سجل الإجازات</button>
                <label for="importFile" class="file-input-label">📁 استيراد نسخة احتياطية</label>
                <input type="file" id="importFile" class="file-input" accept=".json" onchange="importData()">
                <button class="btn btn-warning" onclick="resetData()">🔄 إعادة تعيين</button>
                <button class="btn btn-danger" onclick="clearAllData()">🗑️ مسح الكل</button>
            </div>

            <!-- Quick Stats -->
            <div class="quick-stats" style="margin-top: 20px; display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <div class="quick-stat">
                    <strong>أقسام فعالة:</strong> <span id="activeDepartments">0</span>
                </div>
                <div class="quick-stat">
                    <strong>متوسط سنوات الخدمة:</strong> <span id="avgServiceYears">0</span>
                </div>
                <div class="quick-stat">
                    <strong>إجمالي الإجازات المسجلة:</strong> <span id="totalLeaveRecords">0</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Leave Management Modal -->
    <div id="leaveModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeLeaveModal()">&times;</span>
            <h2>إدارة إجازات: <span id="modalEmployeeName"></span></h2>
            
            <div class="form-row">
                <div class="form-group">
                    <label>نوع الإجازة</label>
                    <select id="leaveType">
                        <option value="annual">إجازة سنوية</option>
                        <option value="sick">إجازة مرضية</option>
                        <option value="emergency">إجازة طارئة</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>عدد الأيام</label>
                    <input type="number" id="leaveDays" min="0.5" step="0.5" placeholder="أدخل عدد الأيام">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>تاريخ البداية</label>
                    <input type="date" id="leaveStartDate">
                </div>
                <div class="form-group">
                    <label>تاريخ النهاية</label>
                    <input type="date" id="leaveEndDate">
                </div>
            </div>
            
            <div class="form-group">
                <label>السبب / الملاحظات</label>
                <input type="text" id="leaveReason" placeholder="أدخل سبب الإجازة (اختياري)">
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <button class="btn btn-success" onclick="addLeaveRecord()">إضافة الإجازة</button>
                <button class="btn" onclick="closeLeaveModal()">إلغاء</button>
            </div>
            
            <div class="leave-history" id="leaveHistory">
                <h4 style="text-align: center; padding: 15px; margin: 0; background: #f8f9fa;">سجل الإجازات</h4>
                <div id="leaveHistoryContent"></div>
            </div>
        </div>
    </div>

    <script>
        let employees = [];
        let filteredEmployees = [];
        let editingIndex = -1;
        let currentEmployeeIndex = -1;

        // Auto-save functionality
        function saveData() {
            try {
                const data = {
                    employees: employees,
                    lastSaved: new Date().toISOString(),
                    version: '2.0'
                };
                
                // Create a JSON string
                const jsonData = JSON.stringify(data, null, 2);
                
                // Create a Blob and save as downloadable file automatically when data changes
                showSaveIndicator('saving');
                
                setTimeout(() => {
                    showSaveIndicator('saved');
                }, 500);
                
                return jsonData;
            } catch (error) {
                console.error('Error saving data:', error);
                showAlert('خطأ في حفظ البيانات', 'danger');
            }
        }

        function showSaveIndicator(status) {
            const indicator = document.getElementById('saveIndicator');
            indicator.className = 'save-indicator';
            
            if (status === 'saving') {
                indicator.className += ' saving';
                indicator.textContent = 'جاري الحفظ...';
            } else if (status === 'saved') {
                indicator.className += ' saved';
                indicator.textContent = 'تم الحفظ ✓';
                setTimeout(() => {
                    indicator.className = 'save-indicator';
                    indicator.textContent = 'محفوظ تلقائياً';
                }, 2000);
            }
        }

        // Load data from file if available
        function loadData() {
            // Initialize with empty data
            employees = [];
            updateTable();
            updateStats();
        }

        // Enhanced alert system
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            
            const icon = type === 'success' ? '✅' : type === 'warning' ? '⚠️' : type === 'danger' ? '❌' : 'ℹ️';
            alert.innerHTML = `${icon} ${message}`;
            
            alertContainer.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 4000);
        }

        // Enhanced employee addition
        function addEmployee() {
            const name = document.getElementById('employeeName').value.trim();
            const id = document.getElementById('employeeId').value.trim();
            const department = document.getElementById('department').value.trim();
            const hireDate = document.getElementById('hireDate').value;
            const annualLeave = parseInt(document.getElementById('annualLeave').value) || 0;
            const sickLeave = parseInt(document.getElementById('sickLeave').value) || 0;
            const emergencyLeave = parseInt(document.getElementById('emergencyLeave').value) || 0;

            // Validation
            if (!name || !id || !department || !hireDate) {
                showAlert('يرجى ملء جميع الحقول المطلوبة المميزة بـ *', 'warning');
                return;
            }

            // Check if employee ID already exists (except when editing)
            if (editingIndex === -1 && employees.some(emp => emp.id === id)) {
                showAlert('الرقم الوظيفي موجود مسبقاً. يرجى استخدام رقم مختلف', 'warning');
                return;
            }

            // Check hire date is not in future
            if (new Date(hireDate) > new Date()) {
                showAlert('تاريخ التوظيف لا يمكن أن يكون في المستقبل', 'warning');
                return;
            }

            const employee = {
                name,
                id,
                department,
                hireDate,
                annualLeave,
                sickLeave,
                emergencyLeave,
                usedAnnual: 0,
                usedSick: 0,
                usedEmergency: 0,
                leaveHistory: []
            };

            if (editingIndex === -1) {
                employees.push(employee);
                showAlert(`تم إضافة الموظف ${name} بنجاح`, 'success');
            } else {
                // Preserve leave history when editing
                employee.leaveHistory = employees[editingIndex].leaveHistory || [];
                employees[editingIndex] = employee;
                showAlert(`تم تحديث بيانات الموظف ${name} بنجاح`, 'success');
                editingIndex = -1;
                document.getElementById('addEmployeeBtn').textContent = 'إضافة موظف';
            }

            clearForm();
            updateTable();
            updateStats();
            saveData();
        }

        function clearForm() {
            document.getElementById('employeeName').value = '';
            document.getElementById('employeeId').value = '';
            document.getElementById('department').value = '';
            document.getElementById('hireDate').value = '';
            document.getElementById('annualLeave').value = '30';
            document.getElementById('sickLeave').value = '15';
            document.getElementById('emergencyLeave').value = '5';
        }

        function calculateYearsOfService(hireDate) {
            const today = new Date();
            const hire = new Date(hireDate);
            const years = (today - hire) / (365.25 * 24 * 60 * 60 * 1000);
            return Math.max(0, Math.floor(years));
        }

        function getEmployeeStatus(employee) {
            const remainingAnnual = employee.annualLeave - employee.usedAnnual;
            if (remainingAnnual <= 0) return 'نفد الرصيد';
            if (remainingAnnual <= 5) return 'رصيد منخفض';
            return 'طبيعي';
        }

        function updateTable() {
            const tbody = document.getElementById('employeeTableBody');
            tbody.innerHTML = '';

            const displayEmployees = filteredEmployees.length > 0 ? filteredEmployees : employees;

            displayEmployees.forEach((employee, index) => {
                const originalIndex = employees.findIndex(emp => emp.id === employee.id);
                const row = tbody.insertRow();
                const yearsOfService = calculateYearsOfService(employee.hireDate);
                const remainingAnnual = employee.annualLeave - employee.usedAnnual;
                const remainingSick = employee.sickLeave - employee.usedSick;
                const remainingEmergency = employee.emergencyLeave - employee.usedEmergency;
                const status = getEmployeeStatus(employee);

                let statusClass = 'balance-positive';
                if (remainingAnnual <= 0) statusClass = 'balance-negative';
                else if (remainingAnnual <= 5) statusClass = 'balance-warning';

                row.innerHTML = `
                    <td>${employee.name}</td>
                    <td>${employee.id}</td>
                    <td>${employee.department}</td>
                    <td>${new Date(employee.hireDate).toLocaleDateString('ar-SA')}</td>
                    <td>${yearsOfService}</td>
                    <td>${employee.annualLeave}</td>
                    <td>${employee.usedAnnual}</td>
                    <td class="leave-balance ${statusClass}">${remainingAnnual}</td>
                    <td class="leave-balance ${remainingSick <= 0 ? 'balance-negative' : remainingSick <= 2 ? 'balance-warning' : 'balance-positive'}">${remainingSick}</td>
                    <td class="leave-balance ${remainingEmergency <= 0 ? 'balance-negative' : 'balance-positive'}">${remainingEmergency}</td>
                    <td class="${statusClass}">${status}</td>
                    <td>
                        <button class="btn btn-small" onclick="editEmployee(${originalIndex})" title="تعديل البيانات">✏️ تعديل</button>
                        <button class="btn btn-success btn-small" onclick="openLeaveModal(${originalIndex})" title="إدارة الإجازات">📅 إجازات</button>
                        <button class="btn btn-danger btn-small" onclick="deleteEmployee(${originalIndex})" title="حذف الموظف">🗑️ حذف</button>
                    </td>
                `;
            });
        }

        function editEmployee(index) {
            const employee = employees[index];
            document.getElementById('employeeName').value = employee.name;
            document.getElementById('employeeId').value = employee.id;
            document.getElementById('department').value = employee.department;
            document.getElementById('hireDate').value = employee.hireDate;
            document.getElementById('annualLeave').value = employee.annualLeave;
            document.getElementById('sickLeave').value = employee.sickLeave;
            document.getElementById('emergencyLeave').value = employee.emergencyLeave;
            
            editingIndex = index;
            document.getElementById('addEmployeeBtn').textContent = 'تحديث الموظف';
            
            // Scroll to top
            window.scrollTo(0, 0);
        }

        function deleteEmployee(index) {
            const employee = employees[index];
            if (confirm(`هل أنت متأكد من حذف الموظف "${employee.name}"؟\nسيتم حذف جميع سجلات الإجازات المرتبطة به.`)) {
                employees.splice(index, 1);
                updateTable();
                updateStats();
                saveData();
                showAlert(`تم حذف الموظف "${employee.name}" بنجاح`, 'success');
            }
        }

        // Enhanced leave management
        function openLeaveModal(index) {
            currentEmployeeIndex = index;
            const employee = employees[index];
            
            document.getElementById('modalEmployeeName').textContent = employee.name;
            document.getElementById('leaveType').value = 'annual';
            document.getElementById('leaveDays').value = '';
            document.getElementById('leaveStartDate').value = '';
            document.getElementById('leaveEndDate').value = '';
            document.getElementById('leaveReason').value = '';
            
            updateLeaveHistory();
            document.getElementById('leaveModal').style.display = 'block';
        }

        function closeLeaveModal() {
            document.getElementById('leaveModal').style.display = 'none';
            currentEmployeeIndex = -1;
        }

        function addLeaveRecord() {
            if (currentEmployeeIndex === -1) return;
            
            const employee = employees[currentEmployeeIndex];
            const leaveType = document.getElementById('leaveType').value;
            const leaveDays = parseFloat(document.getElementById('leaveDays').value);
            const startDate = document.getElementById('leaveStartDate').value;
            const endDate = document.getElementById('leaveEndDate').value;
            const reason = document.getElementById('leaveReason').value.trim();

            // Validation
            if (!leaveDays || leaveDays <= 0) {
                showAlert('يرجى إدخال عدد أيام صحيح', 'warning');
                return;
            }

            if (!startDate || !endDate) {
                showAlert('يرجى تحديد تاريخ البداية والنهاية', 'warning');
                return;
            }

            if (new Date(startDate) > new Date(endDate)) {
                showAlert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'warning');
                return;
            }

            // Check available balance
            let currentUsed = 0;
            let maxAllowed = 0;
            let leaveTypeName = '';

            switch(leaveType) {
                case 'annual':
                    currentUsed = employee.usedAnnual;
                    maxAllowed = employee.annualLeave;
                    leaveTypeName = 'إجازة سنوية';
                    break;
                case 'sick':
                    currentUsed = employee.usedSick;
                    maxAllowed = employee.sickLeave;
                    leaveTypeName = 'إجازة مرضية';
                    break;
                case 'emergency':
                    currentUsed = employee.usedEmergency;
                    maxAllowed = employee.emergencyLeave;
                    leaveTypeName = 'إجازة طارئة';
                    break;
            }

            if (currentUsed + leaveDays > maxAllowed) {
                const remaining = maxAllowed - currentUsed;
                showAlert(`الرصيد غير كافي. المتبقي: ${remaining} يوم فقط`, 'warning');
                return;
            }

            // Add leave record
            const leaveRecord = {
                id: Date.now(),
                type: leaveType,
                typeName: leaveTypeName,
                days: leaveDays,
                startDate,
                endDate,
                reason: reason || 'غير محدد',
                addedDate: new Date().toISOString()
            };

            if (!employee.leaveHistory) {
                employee.leaveHistory = [];
            }
            employee.leaveHistory.push(leaveRecord);

            // Update used days
            switch(leaveType) {
                case 'annual':
                    employee.usedAnnual += leaveDays;
                    break;
                case 'sick':
                    employee.usedSick += leaveDays;
                    break;
                case 'emergency':
                    employee.usedEmergency += leaveDays;
                    break;
            }

            updateTable();
            updateStats();
            updateLeaveHistory();
            saveData();

            showAlert(`تم إضافة ${leaveTypeName} لمدة ${leaveDays} يوم بنجاح`, 'success');

            // Clear form
            document.getElementById('leaveDays').value = '';
            document.getElementById('leaveStartDate').value = '';
            document.getElementById('leaveEndDate').value = '';
            document.getElementById('leaveReason').value = '';
        }

        function updateLeaveHistory() {
            if (currentEmployeeIndex === -1) return;

            const employee = employees[currentEmployeeIndex];
            const historyContent = document.getElementById('leaveHistoryContent');

            if (!employee.leaveHistory || employee.leaveHistory.length === 0) {
                historyContent.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">لا توجد إجازات مسجلة</div>';
                return;
            }

            // Sort by date (newest first)
            const sortedHistory = [...employee.leaveHistory].sort((a, b) => new Date(b.addedDate) - new Date(a.addedDate));

            historyContent.innerHTML = sortedHistory.map(record => `
                <div class="leave-record">
                    <div class="leave-record-header">
                        <strong>${record.typeName}</strong>
                        <button class="delete-leave" onclick="deleteLeaveRecord(${record.id})" title="حذف الإجازة">×</button>
                    </div>
                    <div>📅 من ${new Date(record.startDate).toLocaleDateString('ar-SA')} إلى ${new Date(record.endDate).toLocaleDateString('ar-SA')}</div>
                    <div>⏱️ المدة: ${record.days} يوم</div>
                    <div>📝 السبب: ${record.reason}</div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">تم الإضافة: ${new Date(record.addedDate).toLocaleDateString('ar-SA')}</div>
                </div>
            `).join('');
        }

        function deleteLeaveRecord(recordId) {
            if (currentEmployeeIndex === -1) return;

            const employee = employees[currentEmployeeIndex];
            const recordIndex = employee.leaveHistory.findIndex(record => record.id === recordId);

            if (recordIndex === -1) return;

            const record = employee.leaveHistory[recordIndex];

            if (confirm(`هل أنت متأكد من حذف ${record.typeName} لمدة ${record.days} يوم؟`)) {
                // Restore the used days
                switch(record.type) {
                    case 'annual':
                        employee.usedAnnual -= record.days;
                        break;
                    case 'sick':
                        employee.usedSick -= record.days;
                        break;
                    case 'emergency':
                        employee.usedEmergency -= record.days;
                        break;
                }

                // Remove the record
                employee.leaveHistory.splice(recordIndex, 1);

                updateTable();
                updateStats();
                updateLeaveHistory();
                saveData();

                showAlert('تم حذف الإجازة بنجاح', 'success');
            }
        }

        // Statistics functions
        function updateStats() {
            const totalEmployees = employees.length;
            let totalUsedLeaves = 0;
            let totalRemainingLeaves = 0;
            let totalPossibleLeaves = 0;

            employees.forEach(employee => {
                totalUsedLeaves += employee.usedAnnual + employee.usedSick + employee.usedEmergency;
                totalRemainingLeaves += (employee.annualLeave - employee.usedAnnual) +
                                      (employee.sickLeave - employee.usedSick) +
                                      (employee.emergencyLeave - employee.usedEmergency);
                totalPossibleLeaves += employee.annualLeave + employee.sickLeave + employee.emergencyLeave;
            });

            const averageUsage = totalPossibleLeaves > 0 ? Math.round((totalUsedLeaves / totalPossibleLeaves) * 100) : 0;

            document.getElementById('totalEmployees').textContent = totalEmployees;
            document.getElementById('totalUsedLeaves').textContent = totalUsedLeaves;
            document.getElementById('totalRemainingLeaves').textContent = totalRemainingLeaves;
            document.getElementById('averageUsage').textContent = averageUsage + '%';
        }

        // Export functions
        function exportToCSV() {
            if (employees.length === 0) {
                showAlert('لا توجد بيانات للتصدير', 'warning');
                return;
            }

            const headers = [
                'اسم الموظف',
                'الرقم الوظيفي',
                'القسم',
                'تاريخ التوظيف',
                'سنوات الخدمة',
                'الرصيد السنوي',
                'المستخدم السنوي',
                'المتبقي السنوي',
                'الرصيد المرضي',
                'المستخدم المرضي',
                'المتبقي المرضي',
                'الرصيد الطارئ',
                'المستخدم الطارئ',
                'المتبقي الطارئ',
                'الحالة'
            ];

            const csvContent = [
                headers.join(','),
                ...employees.map(employee => {
                    const yearsOfService = calculateYearsOfService(employee.hireDate);
                    const remainingAnnual = employee.annualLeave - employee.usedAnnual;
                    const remainingSick = employee.sickLeave - employee.usedSick;
                    const remainingEmergency = employee.emergencyLeave - employee.usedEmergency;
                    const status = getEmployeeStatus(employee);

                    return [
                        `"${employee.name}"`,
                        `"${employee.id}"`,
                        `"${employee.department}"`,
                        `"${employee.hireDate}"`,
                        yearsOfService,
                        employee.annualLeave,
                        employee.usedAnnual,
                        remainingAnnual,
                        employee.sickLeave,
                        employee.usedSick,
                        remainingSick,
                        employee.emergencyLeave,
                        employee.usedEmergency,
                        remainingEmergency,
                        `"${status}"`
                    ].join(',');
                })
            ].join('\n');

            // Add BOM for proper Arabic display in Excel
            const BOM = '\uFEFF';
            const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', `تقرير_الإجازات_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showAlert('تم تصدير البيانات بنجاح', 'success');
        }

        function exportToJSON() {
            const data = {
                employees: employees,
                exportDate: new Date().toISOString(),
                version: '2.0',
                totalEmployees: employees.length,
                systemInfo: {
                    name: 'نظام حساب إجازات الموظفين المتطور',
                    version: '2.0',
                    language: 'ar'
                }
            };

            const jsonString = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', `نسخة_احتياطية_${new Date().toISOString().split('T')[0]}.json`);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        }

        function importData() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];

            if (!file) {
                showAlert('يرجى اختيار ملف للاستيراد', 'warning');
                return;
            }

            // File size validation (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                showAlert('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت', 'warning');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                safeExecute(() => {
                    const data = JSON.parse(e.target.result);

                    // Enhanced validation
                    const validationErrors = validateImportData(data);
                    if (validationErrors.length > 0) {
                        showAlert('أخطاء في البيانات:\n' + validationErrors.join('\n'), 'danger');
                        return;
                    }

                    // Sanitize data
                    data.employees = data.employees.map(employee => ({
                        ...employee,
                        name: sanitizeInput(employee.name),
                        id: sanitizeInput(employee.id),
                        department: sanitizeInput(employee.department),
                        // Ensure numeric fields are properly set
                        annualLeave: Math.max(0, parseInt(employee.annualLeave) || 30),
                        sickLeave: Math.max(0, parseInt(employee.sickLeave) || 15),
                        emergencyLeave: Math.max(0, parseInt(employee.emergencyLeave) || 5),
                        usedAnnual: Math.max(0, parseInt(employee.usedAnnual) || 0),
                        usedSick: Math.max(0, parseInt(employee.usedSick) || 0),
                        usedEmergency: Math.max(0, parseInt(employee.usedEmergency) || 0),
                        leaveHistory: Array.isArray(employee.leaveHistory) ? employee.leaveHistory : []
                    }));

                    if (confirm(`سيتم استيراد ${data.employees.length} موظف. هل تريد المتابعة؟\nسيتم استبدال البيانات الحالية.`)) {
                        employees = data.employees;
                        filteredEmployees = []; // Reset filters
                        updateTable();
                        updateStats();
                        updateDepartmentFilter();
                        saveData();
                        showAlert(`تم استيراد ${data.employees.length} موظف بنجاح`, 'success');
                    }
                }, 'خطأ في استيراد البيانات');

                // Clear file input
                fileInput.value = '';
            };

            reader.onerror = function() {
                showAlert('خطأ في قراءة الملف', 'danger');
                fileInput.value = '';
            };

            reader.readAsText(file);
        }

        function resetData() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟\nسيتم حذف جميع الموظفين والإجازات.')) {
                employees = [];
                updateTable();
                updateStats();
                saveData();
                showAlert('تم إعادة تعيين البيانات بنجاح', 'success');
            }
        }

        function clearAllData() {
            if (confirm('تحذير: هذا الإجراء سيحذف جميع البيانات نهائياً!\nهل أنت متأكد من المتابعة؟')) {
                if (confirm('تأكيد أخير: سيتم حذف جميع بيانات الموظفين والإجازات. هل تريد المتابعة؟')) {
                    employees = [];
                    updateTable();
                    updateStats();
                    saveData();
                    showAlert('تم مسح جميع البيانات', 'success');
                }
            }
        }

        // Enhanced data persistence
        function saveToLocalStorage() {
            try {
                const data = {
                    employees: employees,
                    lastSaved: new Date().toISOString(),
                    version: '2.0'
                };
                localStorage.setItem('employeeLeaveSystem', JSON.stringify(data));
                return true;
            } catch (error) {
                console.error('Error saving to localStorage:', error);
                return false;
            }
        }

        function loadFromLocalStorage() {
            try {
                const savedData = localStorage.getItem('employeeLeaveSystem');
                if (savedData) {
                    const data = JSON.parse(savedData);
                    if (data.employees && Array.isArray(data.employees)) {
                        employees = data.employees;
                        updateTable();
                        updateStats();
                        showAlert('تم تحميل البيانات المحفوظة', 'success');
                        return true;
                    }
                }
            } catch (error) {
                console.error('Error loading from localStorage:', error);
            }
            return false;
        }

        // Auto-save with localStorage
        function saveData() {
            try {
                showSaveIndicator('saving');

                // Save to localStorage
                const saved = saveToLocalStorage();

                if (saved) {
                    setTimeout(() => {
                        showSaveIndicator('saved');
                    }, 500);
                } else {
                    showAlert('خطأ في حفظ البيانات محلياً', 'warning');
                }

                return saved;
            } catch (error) {
                console.error('Error saving data:', error);
                showAlert('خطأ في حفظ البيانات', 'danger');
                return false;
            }
        }

        // Enhanced load data function
        function loadData() {
            // Try to load from localStorage first
            if (!loadFromLocalStorage()) {
                // Initialize with empty data if no saved data
                employees = [];
                updateTable();
                updateStats();
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+S to save (export JSON)
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                exportToJSON();
            }

            // Ctrl+E to export CSV
            if (e.ctrlKey && e.key === 'e') {
                e.preventDefault();
                exportToCSV();
            }

            // Escape to close modal
            if (e.key === 'Escape') {
                closeLeaveModal();
            }
        });

        // Click outside modal to close
        window.onclick = function(event) {
            const modal = document.getElementById('leaveModal');
            if (event.target === modal) {
                closeLeaveModal();
            }
        }

        // Auto-calculate leave end date based on start date and days
        document.getElementById('leaveStartDate').addEventListener('change', calculateEndDate);
        document.getElementById('leaveDays').addEventListener('input', calculateEndDate);

        function calculateEndDate() {
            const startDate = document.getElementById('leaveStartDate').value;
            const days = parseFloat(document.getElementById('leaveDays').value);

            if (startDate && days && days > 0) {
                const start = new Date(startDate);
                const end = new Date(start);
                end.setDate(start.getDate() + Math.ceil(days) - 1);

                document.getElementById('leaveEndDate').value = end.toISOString().split('T')[0];
            }
        }

        // Enhanced form validation with security checks
        function validateEmployeeForm() {
            const name = document.getElementById('employeeName').value.trim();
            const id = document.getElementById('employeeId').value.trim();
            const department = document.getElementById('department').value.trim();
            const hireDate = document.getElementById('hireDate').value;
            const annualLeave = parseInt(document.getElementById('annualLeave').value) || 0;
            const sickLeave = parseInt(document.getElementById('sickLeave').value) || 0;
            const emergencyLeave = parseInt(document.getElementById('emergencyLeave').value) || 0;

            const errors = [];

            // Required field validation
            if (!name) errors.push('اسم الموظف مطلوب');
            if (!id) errors.push('الرقم الوظيفي مطلوب');
            if (!department) errors.push('القسم مطلوب');
            if (!hireDate) errors.push('تاريخ التوظيف مطلوب');

            // Format validation
            if (name && (name.length < 2 || name.length > 100)) {
                errors.push('اسم الموظف يجب أن يكون بين 2 و 100 حرف');
            }

            if (id && (id.length < 1 || id.length > 20)) {
                errors.push('الرقم الوظيفي يجب أن يكون بين 1 و 20 حرف');
            }

            if (department && (department.length < 2 || department.length > 50)) {
                errors.push('اسم القسم يجب أن يكون بين 2 و 50 حرف');
            }

            // Security validation - prevent XSS
            const dangerousChars = /<script|javascript:|on\w+=/i;
            if (dangerousChars.test(name) || dangerousChars.test(id) || dangerousChars.test(department)) {
                errors.push('تم اكتشاف محتوى غير آمن في البيانات');
            }

            // Business logic validation
            if (annualLeave < 0 || annualLeave > 365) {
                errors.push('الرصيد السنوي يجب أن يكون بين 0 و 365 يوم');
            }

            if (sickLeave < 0 || sickLeave > 90) {
                errors.push('الإجازة المرضية يجب أن تكون بين 0 و 90 يوم');
            }

            if (emergencyLeave < 0 || emergencyLeave > 30) {
                errors.push('الإجازة الطارئة يجب أن تكون بين 0 و 30 يوم');
            }

            // Date validation
            const hireDateObj = new Date(hireDate);
            const today = new Date();
            const minDate = new Date('1950-01-01');

            if (hireDate && (hireDateObj > today || hireDateObj < minDate)) {
                errors.push('تاريخ التوظيف غير صحيح');
            }

            return errors;
        }

        // Data sanitization function
        function sanitizeInput(input) {
            if (typeof input !== 'string') return input;

            return input
                .trim()
                .replace(/[<>]/g, '') // Remove potential HTML tags
                .replace(/javascript:/gi, '') // Remove javascript: protocol
                .replace(/on\w+=/gi, ''); // Remove event handlers
        }

        // Enhanced data validation for imports
        function validateImportData(data) {
            const errors = [];

            if (!data || typeof data !== 'object') {
                errors.push('بنية الملف غير صحيحة');
                return errors;
            }

            if (!data.employees || !Array.isArray(data.employees)) {
                errors.push('قائمة الموظفين غير موجودة أو غير صحيحة');
                return errors;
            }

            if (data.employees.length > 1000) {
                errors.push('عدد الموظفين يتجاوز الحد المسموح (1000 موظف)');
            }

            data.employees.forEach((employee, index) => {
                if (!employee || typeof employee !== 'object') {
                    errors.push(`بيانات الموظف رقم ${index + 1} غير صحيحة`);
                    return;
                }

                const requiredFields = ['name', 'id', 'department', 'hireDate'];
                requiredFields.forEach(field => {
                    if (!employee[field] || typeof employee[field] !== 'string') {
                        errors.push(`الحقل "${field}" مفقود أو غير صحيح للموظف رقم ${index + 1}`);
                    }
                });

                // Validate numeric fields
                const numericFields = ['annualLeave', 'sickLeave', 'emergencyLeave', 'usedAnnual', 'usedSick', 'usedEmergency'];
                numericFields.forEach(field => {
                    if (employee[field] !== undefined && (typeof employee[field] !== 'number' || employee[field] < 0)) {
                        errors.push(`الحقل "${field}" غير صحيح للموظف رقم ${index + 1}`);
                    }
                });

                // Validate date format
                if (employee.hireDate && isNaN(Date.parse(employee.hireDate))) {
                    errors.push(`تاريخ التوظيف غير صحيح للموظف رقم ${index + 1}`);
                }
            });

            return errors;
        }

        // Performance optimization - debounced functions
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Memory management - cleanup function
        function cleanupMemory() {
            // Remove old alerts
            const alerts = document.querySelectorAll('.alert');
            if (alerts.length > 5) {
                alerts.forEach((alert, index) => {
                    if (index < alerts.length - 5) {
                        alert.remove();
                    }
                });
            }

            // Clear old blob URLs
            if (window.lastBlobUrl) {
                URL.revokeObjectURL(window.lastBlobUrl);
            }
        }

        // Error handling wrapper
        function safeExecute(func, errorMessage = 'حدث خطأ غير متوقع') {
            try {
                return func();
            } catch (error) {
                console.error('Error:', error);
                showAlert(errorMessage, 'danger');
                return null;
            }
        }

        // Enhanced employee addition with better validation
        function addEmployee() {
            return safeExecute(() => {
                const errors = validateEmployeeForm();

                if (errors.length > 0) {
                    showAlert('أخطاء في النموذج:\n' + errors.join('\n'), 'warning');
                    return;
                }

                // Sanitize inputs
                const name = sanitizeInput(document.getElementById('employeeName').value);
                const id = sanitizeInput(document.getElementById('employeeId').value);
                const department = sanitizeInput(document.getElementById('department').value);
                const hireDate = document.getElementById('hireDate').value;
                const annualLeave = Math.max(0, parseInt(document.getElementById('annualLeave').value) || 30);
                const sickLeave = Math.max(0, parseInt(document.getElementById('sickLeave').value) || 15);
                const emergencyLeave = Math.max(0, parseInt(document.getElementById('emergencyLeave').value) || 5);

                // Check if employee ID already exists (except when editing)
                if (editingIndex === -1 && employees.some(emp => emp.id === id)) {
                    showAlert('الرقم الوظيفي موجود مسبقاً. يرجى استخدام رقم مختلف', 'warning');
                    return;
                }

                // Additional business logic checks
                if (employees.length >= 1000) {
                    showAlert('تم الوصول للحد الأقصى من الموظفين (1000 موظف)', 'warning');
                    return;
                }

                const employee = {
                    name,
                    id,
                    department,
                    hireDate,
                    annualLeave,
                    sickLeave,
                    emergencyLeave,
                    usedAnnual: 0,
                    usedSick: 0,
                    usedEmergency: 0,
                    leaveHistory: [],
                    createdDate: new Date().toISOString(),
                    lastModified: new Date().toISOString()
                };

                if (editingIndex === -1) {
                    employees.push(employee);
                    showAlert(`تم إضافة الموظف ${name} بنجاح`, 'success');
                } else {
                    // Preserve leave history and usage when editing
                    const existingEmployee = employees[editingIndex];
                    employee.leaveHistory = existingEmployee.leaveHistory || [];
                    employee.usedAnnual = existingEmployee.usedAnnual || 0;
                    employee.usedSick = existingEmployee.usedSick || 0;
                    employee.usedEmergency = existingEmployee.usedEmergency || 0;
                    employee.createdDate = existingEmployee.createdDate || employee.createdDate;

                    employees[editingIndex] = employee;
                    showAlert(`تم تحديث بيانات الموظف ${name} بنجاح`, 'success');
                    editingIndex = -1;
                    document.getElementById('addEmployeeBtn').textContent = 'إضافة موظف';
                }

                clearForm();
                updateTable();
                updateStats();
                updateDepartmentFilter();
                saveData();
                cleanupMemory(); // Clean up memory after operations
            }, 'خطأ في إضافة/تحديث الموظف');
        }

        // Initialize the system
        document.addEventListener('DOMContentLoaded', function() {
            loadData();

            // Set default hire date to today
            document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];

            showAlert('مرحباً بك في نظام إدارة إجازات الموظفين المتطور', 'success');
        });

        // Search and Filter Functions
        function searchEmployees() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
            const departmentFilter = document.getElementById('departmentFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            if (!searchTerm && !departmentFilter && !statusFilter) {
                filteredEmployees = [];
                updateTable();
                updateResultsInfo();
                return;
            }

            filteredEmployees = employees.filter(employee => {
                const matchesSearch = !searchTerm ||
                    employee.name.toLowerCase().includes(searchTerm) ||
                    employee.id.toLowerCase().includes(searchTerm) ||
                    employee.department.toLowerCase().includes(searchTerm);

                const matchesDepartment = !departmentFilter || employee.department === departmentFilter;
                const matchesStatus = !statusFilter || getEmployeeStatus(employee) === statusFilter;

                return matchesSearch && matchesDepartment && matchesStatus;
            });

            updateTable();
            updateResultsInfo();
        }

        function filterEmployees() {
            searchEmployees(); // Use the same logic
        }

        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('departmentFilter').value = '';
            document.getElementById('statusFilter').value = '';
            filteredEmployees = [];
            updateTable();
            updateResultsInfo();
        }

        function updateResultsInfo() {
            const resultsInfo = document.getElementById('resultsInfo');
            const displayCount = filteredEmployees.length > 0 ? filteredEmployees.length : employees.length;
            const totalCount = employees.length;

            if (filteredEmployees.length > 0) {
                resultsInfo.textContent = `عرض ${displayCount} من أصل ${totalCount} موظف`;
            } else {
                resultsInfo.textContent = `عرض جميع الموظفين (${totalCount})`;
            }
        }

        function updateDepartmentFilter() {
            const departmentFilter = document.getElementById('departmentFilter');
            const currentValue = departmentFilter.value;

            // Get unique departments
            const departments = [...new Set(employees.map(emp => emp.department))].sort();

            // Clear and rebuild options
            departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>';
            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept;
                option.textContent = dept;
                departmentFilter.appendChild(option);
            });

            // Restore previous selection if still valid
            if (departments.includes(currentValue)) {
                departmentFilter.value = currentValue;
            }
        }

        // Dark Mode Functions
        function toggleDarkMode() {
            const body = document.body;
            const isDarkMode = body.classList.toggle('dark-mode');

            // Update toggle button
            const toggleBtn = document.querySelector('.dark-mode-toggle');
            toggleBtn.textContent = isDarkMode ? '☀️' : '🌙';
            toggleBtn.title = isDarkMode ? 'تبديل للوضع العادي' : 'تبديل للوضع المظلم';

            // Save preference
            localStorage.setItem('darkMode', isDarkMode);

            showAlert(isDarkMode ? 'تم تفعيل الوضع المظلم' : 'تم تفعيل الوضع العادي', 'success');
        }

        function loadDarkModePreference() {
            const isDarkMode = localStorage.getItem('darkMode') === 'true';
            if (isDarkMode) {
                document.body.classList.add('dark-mode');
                const toggleBtn = document.querySelector('.dark-mode-toggle');
                toggleBtn.textContent = '☀️';
                toggleBtn.title = 'تبديل للوضع العادي';
            }
        }

        // Enhanced Statistics with Charts (Text-based)
        function updateStats() {
            const totalEmployees = employees.length;
            let totalUsedLeaves = 0;
            let totalRemainingLeaves = 0;
            let totalPossibleLeaves = 0;
            let totalServiceYears = 0;
            let totalLeaveRecords = 0;

            const departments = new Set();

            employees.forEach(employee => {
                totalUsedLeaves += employee.usedAnnual + employee.usedSick + employee.usedEmergency;
                totalRemainingLeaves += (employee.annualLeave - employee.usedAnnual) +
                                      (employee.sickLeave - employee.usedSick) +
                                      (employee.emergencyLeave - employee.usedEmergency);
                totalPossibleLeaves += employee.annualLeave + employee.sickLeave + employee.emergencyLeave;
                totalServiceYears += calculateYearsOfService(employee.hireDate);
                totalLeaveRecords += employee.leaveHistory ? employee.leaveHistory.length : 0;
                departments.add(employee.department);
            });

            const averageUsage = totalPossibleLeaves > 0 ? Math.round((totalUsedLeaves / totalPossibleLeaves) * 100) : 0;
            const avgServiceYears = totalEmployees > 0 ? Math.round(totalServiceYears / totalEmployees) : 0;

            document.getElementById('totalEmployees').textContent = totalEmployees;
            document.getElementById('totalUsedLeaves').textContent = totalUsedLeaves;
            document.getElementById('totalRemainingLeaves').textContent = totalRemainingLeaves;
            document.getElementById('averageUsage').textContent = averageUsage + '%';

            // Update quick stats
            document.getElementById('activeDepartments').textContent = departments.size;
            document.getElementById('avgServiceYears').textContent = avgServiceYears;
            document.getElementById('totalLeaveRecords').textContent = totalLeaveRecords;

            updateResultsInfo();
        }

        // Advanced Report Generation
        function generateAdvancedReport() {
            if (employees.length === 0) {
                showAlert('لا توجد بيانات لإنشاء التقرير', 'warning');
                return;
            }

            const reportData = generateReportData();
            const reportHTML = createAdvancedReportHTML(reportData);

            // Create and download HTML report
            const blob = new Blob([reportHTML], { type: 'text/html;charset=utf-8' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', `تقرير_متقدم_${new Date().toISOString().split('T')[0]}.html`);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showAlert('تم إنشاء التقرير المتقدم بنجاح', 'success');
        }

        function generateReportData() {
            const departments = {};
            const leaveTypes = { annual: 0, sick: 0, emergency: 0 };
            const statusCounts = { normal: 0, warning: 0, critical: 0 };
            let totalServiceYears = 0;

            employees.forEach(employee => {
                // Department statistics
                if (!departments[employee.department]) {
                    departments[employee.department] = {
                        count: 0,
                        totalUsed: 0,
                        totalRemaining: 0,
                        employees: []
                    };
                }

                const dept = departments[employee.department];
                dept.count++;
                dept.totalUsed += employee.usedAnnual + employee.usedSick + employee.usedEmergency;
                dept.totalRemaining += (employee.annualLeave - employee.usedAnnual) +
                                     (employee.sickLeave - employee.usedSick) +
                                     (employee.emergencyLeave - employee.usedEmergency);
                dept.employees.push(employee);

                // Leave type statistics
                leaveTypes.annual += employee.usedAnnual;
                leaveTypes.sick += employee.usedSick;
                leaveTypes.emergency += employee.usedEmergency;

                // Status statistics
                const status = getEmployeeStatus(employee);
                if (status === 'طبيعي') statusCounts.normal++;
                else if (status === 'رصيد منخفض') statusCounts.warning++;
                else statusCounts.critical++;

                totalServiceYears += calculateYearsOfService(employee.hireDate);
            });

            return {
                departments,
                leaveTypes,
                statusCounts,
                totalEmployees: employees.length,
                avgServiceYears: Math.round(totalServiceYears / employees.length),
                generatedDate: new Date().toLocaleDateString('ar-SA')
            };
        }

        function createAdvancedReportHTML(data) {
            return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير إجازات الموظفين المتقدم</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 20px; margin-bottom: 30px; }
        .section { margin: 30px 0; }
        .section h2 { color: #2c3e50; border-bottom: 2px solid #ecf0f1; padding-bottom: 10px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db; }
        .stat-number { font-size: 2em; font-weight: bold; color: #3498db; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: center; border: 1px solid #ddd; }
        th { background: #3498db; color: white; }
        tr:nth-child(even) { background: #f9f9f9; }
        .chart-bar { background: #3498db; height: 20px; border-radius: 10px; margin: 5px 0; }
        @media print { body { background: white; } .container { box-shadow: none; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 تقرير إجازات الموظفين المتقدم</h1>
            <p>تاريخ الإنشاء: ${data.generatedDate}</p>
        </div>

        <div class="section">
            <h2>📊 الإحصائيات العامة</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>إجمالي الموظفين</h3>
                    <div class="stat-number">${data.totalEmployees}</div>
                </div>
                <div class="stat-card">
                    <h3>متوسط سنوات الخدمة</h3>
                    <div class="stat-number">${data.avgServiceYears}</div>
                </div>
                <div class="stat-card">
                    <h3>عدد الأقسام</h3>
                    <div class="stat-number">${Object.keys(data.departments).length}</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🏥 توزيع أنواع الإجازات</h2>
            <table>
                <tr><th>نوع الإجازة</th><th>العدد المستخدم</th><th>النسبة</th></tr>
                <tr><td>إجازة سنوية</td><td>${data.leaveTypes.annual}</td><td>${Math.round((data.leaveTypes.annual / (data.leaveTypes.annual + data.leaveTypes.sick + data.leaveTypes.emergency)) * 100)}%</td></tr>
                <tr><td>إجازة مرضية</td><td>${data.leaveTypes.sick}</td><td>${Math.round((data.leaveTypes.sick / (data.leaveTypes.annual + data.leaveTypes.sick + data.leaveTypes.emergency)) * 100)}%</td></tr>
                <tr><td>إجازة طارئة</td><td>${data.leaveTypes.emergency}</td><td>${Math.round((data.leaveTypes.emergency / (data.leaveTypes.annual + data.leaveTypes.sick + data.leaveTypes.emergency)) * 100)}%</td></tr>
            </table>
        </div>

        <div class="section">
            <h2>🏢 إحصائيات الأقسام</h2>
            <table>
                <tr><th>القسم</th><th>عدد الموظفين</th><th>الإجازات المستخدمة</th><th>الإجازات المتبقية</th></tr>
                ${Object.entries(data.departments).map(([dept, info]) =>
                    `<tr><td>${dept}</td><td>${info.count}</td><td>${info.totalUsed}</td><td>${info.totalRemaining}</td></tr>`
                ).join('')}
            </table>
        </div>

        <div class="section">
            <h2>⚠️ توزيع حالات الموظفين</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>حالة طبيعية</h3>
                    <div class="stat-number" style="color: #27ae60;">${data.statusCounts.normal}</div>
                </div>
                <div class="stat-card">
                    <h3>رصيد منخفض</h3>
                    <div class="stat-number" style="color: #f39c12;">${data.statusCounts.warning}</div>
                </div>
                <div class="stat-card">
                    <h3>نفد الرصيد</h3>
                    <div class="stat-number" style="color: #e74c3c;">${data.statusCounts.critical}</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`;
        }

        function exportLeaveHistory() {
            if (employees.length === 0) {
                showAlert('لا توجد بيانات للتصدير', 'warning');
                return;
            }

            const headers = [
                'اسم الموظف',
                'الرقم الوظيفي',
                'القسم',
                'نوع الإجازة',
                'عدد الأيام',
                'تاريخ البداية',
                'تاريخ النهاية',
                'السبب',
                'تاريخ الإضافة'
            ];

            const rows = [];
            employees.forEach(employee => {
                if (employee.leaveHistory && employee.leaveHistory.length > 0) {
                    employee.leaveHistory.forEach(record => {
                        rows.push([
                            `"${employee.name}"`,
                            `"${employee.id}"`,
                            `"${employee.department}"`,
                            `"${record.typeName}"`,
                            record.days,
                            `"${record.startDate}"`,
                            `"${record.endDate}"`,
                            `"${record.reason}"`,
                            `"${new Date(record.addedDate).toLocaleDateString('ar-SA')}"`
                        ].join(','));
                    });
                }
            });

            if (rows.length === 0) {
                showAlert('لا توجد إجازات مسجلة للتصدير', 'warning');
                return;
            }

            const csvContent = [headers.join(','), ...rows].join('\n');
            const BOM = '\uFEFF';
            const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', `سجل_الإجازات_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showAlert('تم تصدير سجل الإجازات بنجاح', 'success');
        }

        // Enhanced keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+S to save (export JSON)
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                exportToJSON();
            }

            // Ctrl+E to export CSV
            if (e.ctrlKey && e.key === 'e') {
                e.preventDefault();
                exportToCSV();
            }

            // Ctrl+F to focus search
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                document.getElementById('searchInput').focus();
            }

            // Ctrl+D to toggle dark mode
            if (e.ctrlKey && e.key === 'd') {
                e.preventDefault();
                toggleDarkMode();
            }

            // Escape to close modal or clear search
            if (e.key === 'Escape') {
                if (document.getElementById('leaveModal').style.display === 'block') {
                    closeLeaveModal();
                } else {
                    clearFilters();
                }
            }
        });

        // Debounced search for better performance
        const debouncedSearch = debounce(searchEmployees, 300);

        // Performance monitoring
        function measurePerformance(name, func) {
            const start = performance.now();
            const result = func();
            const end = performance.now();
            console.log(`${name} took ${end - start} milliseconds`);
            return result;
        }

        // Optimized table update with virtual scrolling concept
        function updateTableOptimized() {
            return measurePerformance('Table Update', () => {
                const tbody = document.getElementById('employeeTableBody');
                const fragment = document.createDocumentFragment();

                const displayEmployees = filteredEmployees.length > 0 ? filteredEmployees : employees;

                // Clear existing content
                tbody.innerHTML = '';

                displayEmployees.forEach((employee, index) => {
                    const originalIndex = employees.findIndex(emp => emp.id === employee.id);
                    const row = createEmployeeRow(employee, originalIndex);
                    fragment.appendChild(row);
                });

                tbody.appendChild(fragment);
            });
        }

        function createEmployeeRow(employee, originalIndex) {
            const row = document.createElement('tr');
            const yearsOfService = calculateYearsOfService(employee.hireDate);
            const remainingAnnual = employee.annualLeave - employee.usedAnnual;
            const remainingSick = employee.sickLeave - employee.usedSick;
            const remainingEmergency = employee.emergencyLeave - employee.usedEmergency;
            const status = getEmployeeStatus(employee);

            let statusClass = 'balance-positive';
            if (remainingAnnual <= 0) statusClass = 'balance-negative';
            else if (remainingAnnual <= 5) statusClass = 'balance-warning';

            row.innerHTML = `
                <td>${employee.name}</td>
                <td>${employee.id}</td>
                <td>${employee.department}</td>
                <td>${new Date(employee.hireDate).toLocaleDateString('ar-SA')}</td>
                <td>${yearsOfService}</td>
                <td>${employee.annualLeave}</td>
                <td>${employee.usedAnnual}</td>
                <td class="leave-balance ${statusClass}">${remainingAnnual}</td>
                <td class="leave-balance ${remainingSick <= 0 ? 'balance-negative' : remainingSick <= 2 ? 'balance-warning' : 'balance-positive'}">${remainingSick}</td>
                <td class="leave-balance ${remainingEmergency <= 0 ? 'balance-negative' : 'balance-positive'}">${remainingEmergency}</td>
                <td class="${statusClass}">${status}</td>
                <td>
                    <button class="btn btn-small tooltip" onclick="editEmployee(${originalIndex})" title="تعديل البيانات">
                        ✏️ تعديل
                        <span class="tooltiptext">تعديل بيانات الموظف</span>
                    </button>
                    <button class="btn btn-success btn-small tooltip" onclick="openLeaveModal(${originalIndex})" title="إدارة الإجازات">
                        📅 إجازات
                        <span class="tooltiptext">إدارة إجازات الموظف</span>
                    </button>
                    <button class="btn btn-danger btn-small tooltip" onclick="deleteEmployee(${originalIndex})" title="حذف الموظف">
                        🗑️ حذف
                        <span class="tooltiptext">حذف الموظف نهائياً</span>
                    </button>
                </td>
            `;

            return row;
        }

        // Replace the original updateTable with optimized version
        function updateTable() {
            return updateTableOptimized();
        }

        // Initialize the system
        document.addEventListener('DOMContentLoaded', function() {
            // Performance measurement for initialization
            measurePerformance('System Initialization', () => {
                loadDarkModePreference();
                loadData();
                updateDepartmentFilter();

                // Set default hire date to today
                document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];

                // Setup event listeners with debouncing
                document.getElementById('searchInput').addEventListener('input', debouncedSearch);

                // Setup form validation on input
                const inputs = ['employeeName', 'employeeId', 'department'];
                inputs.forEach(inputId => {
                    const input = document.getElementById(inputId);
                    if (input) {
                        input.addEventListener('blur', () => {
                            const errors = validateEmployeeForm();
                            if (errors.length > 0) {
                                input.style.borderColor = '#e74c3c';
                            } else {
                                input.style.borderColor = '#27ae60';
                            }
                        });
                    }
                });

                showAlert('مرحباً بك في نظام إدارة إجازات الموظفين المتطور', 'success');

                // Show keyboard shortcuts info
                setTimeout(() => {
                    showAlert('💡 اختصارات لوحة المفاتيح: Ctrl+S (حفظ), Ctrl+E (تصدير), Ctrl+F (بحث), Ctrl+D (وضع مظلم)', 'info');
                }, 3000);

                // Setup periodic cleanup
                setInterval(cleanupMemory, 5 * 60 * 1000); // Every 5 minutes
            });
        });

        // Periodic auto-save every 5 minutes
        setInterval(function() {
            if (employees.length > 0) {
                saveData();
            }
        }, 5 * 60 * 1000);
    </script>
</body>
</html>